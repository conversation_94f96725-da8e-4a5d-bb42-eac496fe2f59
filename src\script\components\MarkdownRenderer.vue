<template>
    <div
        class="markdown-content"
        :data-message-index="messageIndex"
        v-html="getRenderedContent(content, messageIndex)"
    ></div>
</template>

<script>
const marked = require('marked');
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-light.css';

export default {
    name: 'MarkdownRenderer',
    props: {
        content: {
            type: String,
            required: true,
            default: ''
        },
        messageIndex: {
            type: Number,
            required: true,
            default: 0
        },
        sessionId: {
            type: String,
            default: null
        }
    },
    data() {
        return {
            renderedContents: new Map() // 缓存渲染，按会话隔离
        };
    },
    watch: {
        content: {
            handler() {
                // 清理当前会话的缓存
                this.clearSessionCache();
                this.$nextTick(() => {
                    this.addInteractiveFeatures();
                });
            },
            immediate: true
        },
        sessionId: {
            handler(newSessionId, oldSessionId) {
                if (newSessionId !== oldSessionId) {
                    // 会话切换时清理旧会话的缓存
                    this.clearSessionCache(oldSessionId);
                }
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.addInteractiveFeatures();
        });
    },
    updated() {
        // 组件更新后添加交互功能
        this.$nextTick(() => {
            this.addInteractiveFeatures();
        });
    },
    beforeDestroy() {
        // 组件销毁前清理当前会话的缓存
        this.clearSessionCache();
    },
    methods: {
        // 获取渲染后的内容（用于v-html）
        getRenderedContent(text, messageIndex) {
            if (!text || typeof text !== 'string') {
                return '';
            }

            // 检查是否为流式响应中的消息
            const isStreaming = this.isStreamingMessage(messageIndex);

            if (isStreaming) {
                // 流式响应：实现逐字符渲染
                return this.getStreamingRenderedContent(text, messageIndex);
            }
            // 非流式响应：使用完整缓存策略
            return this.getCompleteRenderedContent(text, messageIndex);
        },

        // 流式响应的逐字符渲染
        getStreamingRenderedContent(text, messageIndex) {
            const textLength = text.length;
            const sessionId = this.getCurrentSessionId();

            // 使用会话隔离的缓存策略，每5个字符一个缓存点
            const cacheKey = `stream-${sessionId}-${messageIndex}-${Math.floor(textLength / 5)}`;

            // 检查近期缓存
            if (this.renderedContents.has(cacheKey)) {
                const cached = this.renderedContents.get(cacheKey);
                if (cached.length <= textLength) {
                    // 如果缓存的内容长度合适，尝试增量渲染
                    return this.incrementalRender(text, cached.content);
                }
            }

            try {
                // 对于流式响应，使用优化的渲染策略
                const renderedHtml = this.renderMarkdownOptimized(text, true);

                // 存储渲染结果
                this.renderedContents.set(cacheKey, {
                    content: renderedHtml,
                    length: textLength
                });

                // 限制流式缓存大小
                if (this.renderedContents.size > 30) {
                    // 清理旧的流式缓存
                    this.cleanupStreamCache(messageIndex);
                }

                return renderedHtml;
            } catch (error) {
                console.error('流式Markdown渲染错误:', error);
                return this.escapeHtml(text);
            }
        },

        // 完整内容渲染（非流式）
        getCompleteRenderedContent(text, messageIndex) {
            const textLength = text.length;
            const textHash = this.getTextHash(text);
            const sessionId = this.getCurrentSessionId();
            const cacheKey = `complete-${sessionId}-${messageIndex}-${textLength}-${textHash}`;

            // 检查缓存
            if (this.renderedContents.has(cacheKey)) {
                return this.renderedContents.get(cacheKey);
            }

            try {
                const renderedHtml = this.renderMarkdown(text);

                // 存储渲染结果
                this.renderedContents.set(cacheKey, renderedHtml);

                // 限制缓存大小
                if (this.renderedContents.size > 50) {
                    const firstKey = this.renderedContents.keys().next().value;
                    this.renderedContents.delete(firstKey);
                }

                return renderedHtml;
            } catch (error) {
                console.error('Markdown渲染错误:', error);
                return text;
            }
        },

        // 增量渲染逻辑
        incrementalRender(newText, cachedHtml) {
            // 如果新文本只是在末尾添加了少量字符，尝试增量更新
            const newLength = newText.length;

            // 简单策略：如果文本变化不大，直接重新渲染
            // 这里可以根据需要实现更复杂的增量渲染逻辑
            if (newLength > 0) {
                try {
                    return this.renderMarkdownOptimized(newText, true);
                } catch (error) {
                    console.error('增量渲染错误:', error);
                    return cachedHtml; // 返回缓存内容
                }
            }

            return cachedHtml;
        },

        // 优化的 Markdown 渲染方法
        renderMarkdownOptimized(text, isStreaming = false) {
            if (!text) return '';

            // 对于流式响应，使用更快的渲染策略
            if (isStreaming) {
                // 检查是否包含复杂的 Markdown 语法
                const hasComplexSyntax = /```|`[^`]+`|\*\*|\*|__|_|\[.*\]\(.*\)|#{1,6}\s/.test(
                    text
                );

                if (!hasComplexSyntax) {
                    // 简单文本，直接转义并返回
                    return this.escapeHtml(text).replace(/\n/g, '<br>');
                }
            }

            // 使用完整的 Markdown 渲染
            return this.renderMarkdown(text);
        },

        // 清理流式缓存
        cleanupStreamCache(currentMessageIndex) {
            const sessionId = this.getCurrentSessionId();
            const keysToDelete = [];
            for (const key of this.renderedContents.keys()) {
                if (
                    key.startsWith('stream-') &&
                    !key.includes(`stream-${sessionId}-${currentMessageIndex}-`)
                ) {
                    keysToDelete.push(key);
                }
            }

            // 删除其他消息的流式缓存
            keysToDelete.forEach((key) => {
                this.renderedContents.delete(key);
            });
        },

        // 清理会话缓存
        clearSessionCache(sessionId = null) {
            const targetSessionId = sessionId || this.getCurrentSessionId();
            if (!targetSessionId) {
                // 如果没有会话ID，清理所有缓存
                this.renderedContents.clear();
                return;
            }

            const keysToDelete = [];
            for (const key of this.renderedContents.keys()) {
                if (key.includes(`-${targetSessionId}-`)) {
                    keysToDelete.push(key);
                }
            }

            keysToDelete.forEach((key) => {
                this.renderedContents.delete(key);
            });
        },

        // 获取当前会话ID
        getCurrentSessionId() {
            // 优先使用props传入的sessionId
            if (this.sessionId) {
                return this.sessionId;
            }

            // 从父组件获取
            if (this.$parent && this.$parent.sessionId) {
                return this.$parent.sessionId;
            }

            // 从Vuex获取
            if (this.$store && this.$store.state.dua_currentSessionId) {
                return this.$store.state.dua_currentSessionId;
            }

            return 'default';
        },

        // HTML 转义方法
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // 检查是否为流式消息（需要从父组件传入）
        isStreamingMessage(messageIndex) {
            // 通过 props 或事件获取流式状态
            if (this.$parent && this.$parent.isStreamingMessage) {
                return this.$parent.isStreamingMessage(messageIndex);
            }
            return false;
        },

        // 生成文本哈希（优化版本，只对较长文本计算哈希）
        getTextHash(text) {
            // 对于短文本，直接使用长度作为标识
            if (text.length < 100) {
                return text.length.toString();
            }

            // 对于长文本，计算简单哈希
            let hash = 0;
            const step = Math.max(1, Math.floor(text.length / 50)); // 采样计算
            for (let i = 0; i < text.length; i += step) {
                const char = text.charCodeAt(i);
                hash = (hash << 5) - hash + char;
                hash = hash & hash; // 转换为32位整数
            }
            return hash.toString();
        },

        // 添加交互功能到所有消息
        addInteractiveFeatures() {
            const element = this.$el;
            if (element) {
                this.addCodeCopyButtons(element);
                this.addTableDownloadButtons(element);
            }
        },

        // 渲染Markdown内容
        renderMarkdown(content) {
            // 配置marked选项
            marked.setOptions({
                highlight: (code, language) => {
                    if (
                        language &&
                        this.isValidLanguageIdentifier(language) &&
                        hljs.getLanguage(language)
                    ) {
                        try {
                            return hljs.highlight(language, code).value;
                        } catch (err) {
                            console.warn('代码高亮失败:', err);
                        }
                    }
                    return hljs.highlightAuto(code).value;
                },
                breaks: true,
                gfm: true,
                tables: true,
                sanitize: false,
                renderer: this.createCustomRenderer()
            });

            return marked(content);
        },

        // 创建自定义渲染器
        createCustomRenderer() {
            const renderer = new marked.Renderer();

            // 自定义代码块渲染
            renderer.code = (code, language) => {
                const validLanguage = language && this.isValidLanguageIdentifier(language);
                let displayLanguage;
                if (validLanguage) {
                    displayLanguage = language.toLowerCase();
                } else {
                    displayLanguage = '代码';
                }

                // 高亮代码
                let highlightedCode;
                if (validLanguage && hljs.getLanguage(language)) {
                    try {
                        highlightedCode = hljs.highlight(language, code).value;
                    } catch (err) {
                        console.warn('代码高亮失败:', err);
                        highlightedCode = hljs.highlightAuto(code).value;
                    }
                } else {
                    highlightedCode = hljs.highlightAuto(code).value;
                }

                return `
                    <div class="code-block-wrapper">
                        <div class="code-block-header">
                            <span class="code-language">${this.escapeHtml(displayLanguage)}</span>
                            <button class="copy-button" title="复制代码">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                    <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                </svg>
                                <span class="copy-text">复制</span>
                            </button>
                        </div>
                        <pre><code>${highlightedCode}</code></pre>
                    </div>
                `;
            };

            return renderer;
        },

        // 验证语言标识符是否有效
        isValidLanguageIdentifier(lang) {
            if (!lang || typeof lang !== 'string') {
                return false;
            }

            const normalizedLang = lang.toLowerCase().trim();

            // 语言标识符应该是简短的单词，不应包含代码内容
            return !(
                normalizedLang.length > 20 ||
                normalizedLang.includes('(') ||
                normalizedLang.includes(')') ||
                normalizedLang.includes('{') ||
                normalizedLang.includes('}') ||
                normalizedLang.includes(' ') ||
                normalizedLang.includes('\n') ||
                normalizedLang.includes(';') ||
                normalizedLang.includes('=') ||
                normalizedLang.includes('[') ||
                normalizedLang.includes(']')
            );
        },

        // 添加代码复制按钮
        addCodeCopyButtons(container) {
            // 处理新结构的代码块（带header的）
            const codeBlockWrappers = container.querySelectorAll('.code-block-wrapper');
            codeBlockWrappers.forEach((wrapper) => {
                const copyButton = wrapper.querySelector('.copy-button');
                if (copyButton && !copyButton.hasAttribute('data-listener-added')) {
                    copyButton.addEventListener('click', () => {
                        const code = wrapper.querySelector('pre code');
                        if (code) {
                            this.copyToClipboard(code.textContent);
                            this.showCopySuccess(copyButton);
                        }
                    });
                    copyButton.setAttribute('data-listener-added', 'true');
                }
            });

            // 处理旧结构的代码块（没有header的，兼容性处理）
            const preElements = container.querySelectorAll('pre:not(.code-block-wrapper pre)');
            preElements.forEach((pre) => {
                if (pre.querySelector('.copy-button')) return; // 避免重复添加

                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button';
                copyButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                    </svg>
                    <span class="copy-text">复制</span>
                `;
                copyButton.title = '复制代码';

                copyButton.addEventListener('click', () => {
                    const code = pre.querySelector('code');
                    if (code) {
                        this.copyToClipboard(code.textContent);
                        this.showCopySuccess(copyButton);
                    }
                });

                pre.style.position = 'relative';
                pre.appendChild(copyButton);
            });
        },

        // 添加表格下载按钮
        addTableDownloadButtons(container) {
            const tables = container.querySelectorAll('table');
            tables.forEach((table, index) => {
                if (table.parentNode.querySelector('.table-download-button')) return; // 避免重复添加

                const tableWrapper = document.createElement('div');
                tableWrapper.className = 'table-wrapper';

                const downloadButton = document.createElement('button');
                downloadButton.className = 'table-download-button';
                downloadButton.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    <span class="download-text">下载</span>
                `;
                downloadButton.title = '下载表格为Excel文件';

                downloadButton.addEventListener('click', () => {
                    this.downloadTableAsExcel(table, `智能助手导出表格_${index + 1}`);
                });

                // 包装表格
                table.parentNode.insertBefore(tableWrapper, table);
                tableWrapper.appendChild(table);
                tableWrapper.appendChild(downloadButton);
            });
        },

        // 复制到剪贴板
        copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                document.execCommand('copy');
                textArea.remove();
            }
        },

        // 显示复制成功状态
        showCopySuccess(button) {
            const originalHtml = button.innerHTML;
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                <span class="copy-text">已复制</span>
            `;
            button.classList.add('copied');

            setTimeout(() => {
                button.innerHTML = originalHtml;
                button.classList.remove('copied');
            }, 2000);
        },

        // 下载表格为Excel
        downloadTableAsExcel(table, filename) {
            // 创建工作簿
            const rows = [];
            const headerRow = [];

            // 提取表头
            const headers = table.querySelectorAll('thead th');
            headers.forEach((th) => {
                headerRow.push(th.textContent.trim());
            });
            if (headerRow.length > 0) {
                rows.push(headerRow);
            }

            // 提取数据行
            const bodyRows = table.querySelectorAll('tbody tr');
            bodyRows.forEach((tr) => {
                const row = [];
                const cells = tr.querySelectorAll('td');
                cells.forEach((td) => {
                    row.push(td.textContent.trim());
                });
                if (row.length > 0) {
                    rows.push(row);
                }
            });

            // 创建CSV内容
            const csvContent = rows
                .map((row) => row.map((cell) => `"${cell.replace(/"/g, '""')}"`).join(','))
                .join('\n');

            // 添加BOM以支持中文
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csvContent], {
                type: 'text/csv;charset=utf-8;'
            });

            // 创建下载链接
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }
};
</script>

<style lang="less">
.markdown-content {
    line-height: 1.6;
    color: #333;

    // 标题样式 - 优化层次和缩进
    h1 {
        font-size: 24px;
        font-weight: bold;
        margin: 20px 0 16px 0;
        padding-left: 0;
        color: #2c3e50;
        border-bottom: 2px solid #3498db;
        padding-bottom: 8px;
        line-height: 1.3;

        &:first-child {
            margin-top: 0;
        }
    }

    h2 {
        font-size: 20px;
        font-weight: bold;
        margin: 18px 0 14px 0;
        padding-left: 8px;
        color: #34495e;
        border-bottom: 1px solid #bdc3c7;
        padding-bottom: 6px;
        line-height: 1.3;
    }

    h3 {
        font-size: 18px;
        font-weight: bold;
        margin: 16px 0 12px 0;
        padding-left: 16px;
        color: #34495e;
        line-height: 1.3;
    }

    h4 {
        font-size: 16px;
        font-weight: bold;
        margin: 14px 0 10px 0;
        padding-left: 24px;
        color: #34495e;
        line-height: 1.3;
    }

    h5,
    h6 {
        font-size: 15px;
        font-weight: bold;
        margin: 12px 0 8px 0;
        padding-left: 32px;
        color: #34495e;
        line-height: 1.3;
    }

    // 段落样式 - 优化缩进和间距
    p {
        margin: 12px 0;
        padding-left: 8px;
        line-height: 1.7;
        text-indent: 0;
        color: #333;

        &:first-child {
            margin-top: 0;
        }

        &:last-child {
            margin-bottom: 0;
        }

        // 嵌套在列表中的段落
        li & {
            margin: 4px 0;
            padding-left: 0;
        }

        // 嵌套在引用中的段落
        blockquote & {
            padding-left: 0;
        }
    }

    // 强调样式
    strong {
        font-weight: bold;
        color: #2c3e50;
    }

    em {
        font-style: italic;
        color: #7f8c8d;
    }

    // 列表样式 - 优化缩进层次
    ul,
    ol {
        margin: 12px 0;
        padding-left: 32px;

        li {
            margin: 6px 0;
            line-height: 1.6;
            padding-left: 4px;

            // 嵌套列表
            ul,
            ol {
                margin: 6px 0;
                padding-left: 24px;

                li {
                    margin: 4px 0;

                    // 三级嵌套
                    ul,
                    ol {
                        padding-left: 20px;
                    }
                }
            }
        }
    }

    // 链接样式
    a {
        color: #0265fe;
        text-decoration: none;
        border-bottom: 1px solid transparent;
        transition: all 0.2s ease;

        &:hover {
            border-bottom-color: #0265fe;
        }
    }

    // 行内代码样式
    code {
        background: rgba(175, 184, 193, 0.2);
        color: #476582;
        padding: 3px 6px;
        border-radius: 4px;
        font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
            'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
        font-size: 0.9em;
        font-weight: 500;
        border: 1px solid rgba(175, 184, 193, 0.3);
        letter-spacing: 0.025em;
    }

    // 代码块包装器样式
    .code-block-wrapper {
        margin: 16px 0;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(175, 184, 193, 0.3);
        backdrop-filter: blur(10px);
    }

    // 代码块头部样式
    .code-block-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.8);
        border-bottom: 1px solid rgba(175, 184, 193, 0.2);
        backdrop-filter: blur(5px);

        .code-language {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
                'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        }

        .copy-button {
            background: transparent;
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 12px;
            color: #0265fe;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
                'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            font-weight: 500;

            &:hover {
                background: rgba(2, 101, 254, 0.08);
            }

            &.copied {
                color: #28a745;

                &:hover {
                    background: rgba(40, 167, 69, 0.08);
                }
            }

            svg {
                width: 14px;
                height: 14px;
                flex-shrink: 0;
            }

            .copy-text {
                font-weight: 500;
                white-space: nowrap;
            }
        }
    }

    // 代码块样式（在包装器内）
    .code-block-wrapper pre {
        background: none;
        border: none;
        border-radius: 0;
        padding: 16px;
        margin: 0;
        overflow-x: auto;
        position: relative;
        box-shadow: none;
        backdrop-filter: none;

        code {
            background: none;
            color: #2d3748;
            padding: 0;
            border: none;
            border-radius: 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
                'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            letter-spacing: 0.025em;
        }
    }

    // 兼容旧的代码块样式（没有header的）
    pre:not(.code-block-wrapper pre) {
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(175, 184, 193, 0.3);
        border-radius: 10px;
        padding: 16px;
        margin: 16px 0;
        overflow-x: auto;
        position: relative;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        backdrop-filter: blur(10px);

        code {
            background: none;
            color: #2d3748;
            padding: 0;
            border: none;
            border-radius: 0;
            font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata',
                'Roboto Mono', 'Source Code Pro', 'Menlo', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.5;
            font-weight: 400;
            letter-spacing: 0.025em;
        }
    }

    // 旧式代码复制按钮样式（兼容性）
    pre:not(.code-block-wrapper pre) .copy-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        font-weight: 500;

        &:hover {
            background: rgba(2, 101, 254, 0.08);
            border-radius: 6px;
        }

        &.copied {
            color: #28a745;

            &:hover {
                background: rgba(40, 167, 69, 0.08);
            }
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .copy-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    // 表格包装器样式
    .table-wrapper {
        position: relative;
        margin: 16px 0;
    }

    // 引用样式 - 优化设计
    blockquote {
        border-left: 4px solid #0265fe;
        margin: 16px 0;
        padding: 16px 20px;
        background: rgba(2, 101, 254, 0.05);
        border-radius: 0 10px 10px 0;
        color: #555;
        font-style: italic;
        backdrop-filter: blur(5px);

        p {
            margin: 0;
            padding-left: 0;
        }
    }

    // 表格样式 - 重新设计
    table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin: 16px 0;
        background-color: transparent;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);

        thead {
            background: rgba(255, 255, 255, 0.8);
        }

        th {
            padding: 14px 18px;
            text-align: left;
            font-weight: 600;
            color: #2c3e50;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);

            // 移除竖向分割线
            border-left: none;
            border-right: none;

            &:first-child {
                border-top-left-radius: 10px;
            }

            &:last-child {
                border-top-right-radius: 10px;
            }
        }

        td {
            padding: 14px 18px;
            border-bottom: 1px solid #e8e8e8;
            font-size: 14px;
            line-height: 1.6;
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(5px);

            // 移除竖向分割线
            border-left: none;
            border-right: none;
        }

        tbody tr:hover td {
            background: rgba(255, 255, 255, 0.8);
        }

        tbody tr:last-child td {
            border-bottom: none;

            &:first-child {
                border-bottom-left-radius: 10px;
            }

            &:last-child {
                border-bottom-right-radius: 10px;
            }
        }
    }

    // 表格下载按钮样式 - 统一设计
    .table-download-button {
        position: absolute;
        top: 12px;
        right: 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        padding: 5px 10px;
        font-size: 13px;
        color: #0265fe;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 4px;
        transition: all 0.2s ease;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
            'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
        z-index: 10;
        font-weight: 500;

        &:hover {
            background: rgba(2, 101, 254, 0.08);
            border-radius: 6px;
        }

        svg {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }

        .download-text {
            font-weight: 500;
            white-space: nowrap;
        }
    }

    // 分割线样式
    hr {
        border: none;
        height: 1px;
        background: #e8e8e8;
        margin: 16px 0;
    }

    // 图片样式
    img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 8px 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 复选框列表样式
    input[type='checkbox'] {
        margin-right: 8px;
        transform: scale(1.2);
    }
}
</style>
