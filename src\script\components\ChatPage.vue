<template>
    <div class="chat-page">
        <div class="chat-title-section">
            <div
                v-if="!isEditingTitle"
                class="chat-title"
                :class="{ 'temporary-session': sessionId && sessionId.startsWith('temp_') }"
                @click="startEditTitle"
                :title="
                    sessionId && sessionId.startsWith('temp_')
                        ? '发送消息后可编辑标题'
                        : '点击编辑标题'
                "
            >
                {{ displayTitle }}
                <i
                    v-if="sessionId && !sessionId.startsWith('temp_')"
                    class="el-icon-edit-outline edit-icon"
                ></i>
            </div>
            <div v-else class="chat-title-editor">
                <el-input
                    ref="titleInput"
                    v-model="editingTitleValue"
                    size="small"
                    :maxlength="50"
                    @blur="saveTitle"
                    @keyup.enter.native="saveTitle"
                    @keyup.esc.native="cancelEditTitle"
                    placeholder="输入对话标题"
                />
            </div>
        </div>
        <div class="chat-container">
            <!-- 聊天消息区域 -->
            <div class="chat-messages custom-scrollbar" ref="messagesContainer">
                <div
                    v-for="(message, index) in messages"
                    :key="index"
                    class="message-item"
                    :class="{
                        'user-message': message.type === 'user',
                        'assistant-message': message.type === 'assistant',
                        'error-message': message.isError
                    }"
                >
                    <div class="message-content">
                        <!-- 用户消息：纯文本显示 -->
                        <div v-if="message.type === 'user'" class="message-text">
                            {{ message.text }}
                        </div>
                        <!-- AI助手消息：Markdown -->
                        <div v-if="message.type === 'assistant'" class="message-text-container">
                            <MarkdownRenderer
                                v-if="getDisplayText(message)"
                                :content="getDisplayText(message)"
                                :message-index="index"
                                :session-id="sessionId"
                                class="message-text"
                                :class="{ 'streaming-text': isStreamingMessage(index) }"
                            />
                        </div>
                    </div>
                </div>

                <!-- 加载状态 - 只在当前会话正在响应时显示 -->
                <div
                    v-if="isAiResponding && shouldShowLoadingState"
                    class="message-item assistant-message"
                >
                    <div class="message-content loading-message">
                        <div class="typing-text">正在调用知识问答服务，请稍候…</div>
                        <div class="typing-dots">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                        <div class="stop-generation-btn" @click="handleStopGeneration">
                            停止生成
                        </div>
                    </div>
                </div>

                <!-- 空状态提示 -->
                <div v-if="messages.length === 0 && !isAiResponding" class="empty-state">
                    <div class="empty-icon">
                        <img src="~@/img/main/logo-big.png" alt="logo" />
                    </div>
                    <p class="empty-text">开始您的对话吧</p>
                </div>
            </div>

            <!-- 输入区域 -->
            <div class="chat-input-section">
                <MessageInput
                    :placeholder="placeholder"
                    :model-options="modelOptions"
                    :defaultModel="defaultModel"
                    :selectedModel="selectedModel"
                    :show-mic-button="true"
                    :show-model-details="true"
                    :disabled="isAiResponding"
                    popper-class="dataUsageAssistant-theme model-select-popper"
                    @send-message="handleSendMessage"
                    @mic-click="handleMicClick"
                    @model-change="handleModelChange"
                />
                <!-- 回到底部 -->
                <div
                    class="back-bottom"
                    :class="{ 'is-show': showBackToBottom }"
                    @click="scrollToBottom"
                >
                    <i class="el-icon-arrow-down"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import MessageInput from './MessageInput.vue';
import MarkdownRenderer from './MarkdownRenderer.vue';

export default {
    name: 'ChatPage',
    components: {
        MessageInput,
        MarkdownRenderer
    },
    props: {
        // 聊天标题
        title: {
            type: String,
            default: '新对话'
        },
        // 当前会话ID
        sessionId: {
            type: String,
            default: null
        },
        // 聊天消息列表
        messages: {
            type: Array,
            default: () => []
        },
        modelOptions: {
            type: Array,
            default: () => []
        },
        defaultModel: {
            type: String,
            default: ''
        },
        selectedModel: {
            type: String,
            default: ''
        },
        placeholder: {
            type: String,
            default: '请输入您的问题...'
        }
    },
    data() {
        return {
            // 控制回到底部按钮显示状态
            showBackToBottom: false,
            // 滚动监听器的节流定时器
            scrollThrottleTimer: null,
            // 标题编辑相关状态
            isEditingTitle: false,
            editingTitleValue: '',
            // 流式响应相关状态
            lastScrollTime: 0 // 上次滚动时间，用于节流
        };
    },
    computed: {
        // 显示的标题
        displayTitle() {
            return this.title || '新对话';
        },

        // 获取AI响应状态（会话级隔离）
        isAiResponding() {
            return this.$store.getters.dua_isCurrentSessionResponding;
        },

        // 获取流式响应状态（会话级隔离）
        isStreamResponse() {
            return this.$store.getters.dua_isCurrentSessionStreaming;
        },

        // 获取当前响应的会话ID
        respondingSessionId() {
            return this.$store.state.dua_globalRespondingSessionId;
        },

        // 是否应该显示加载状态（防止会话间串扰）
        shouldShowLoadingState() {
            // 使用 Vuex getter，确保状态一致性
            const shouldShow = this.$store.getters.dua_shouldShowLoadingState;

            // 额外检查：确保当前会话ID与响应中的会话ID一致
            const respondingSessionId = this.$store.state.dua_globalRespondingSessionId;
            const isCurrentSession = !respondingSessionId || respondingSessionId === this.sessionId;

            // 检查全局AI响应状态，确保没有被取消
            const globalAiResponding = this.$store.state.dua_globalAiResponding;
            const globalStreamResponse = this.$store.state.dua_globalStreamResponse;

            // 只有在全局状态确实在响应且是当前会话时才显示加载状态
            return shouldShow && isCurrentSession && (globalAiResponding || globalStreamResponse);
        },

        // 当前会话是否有正在进行的流式响应
        hasActiveStreaming() {
            // 使用 Vuex getter，确保状态一致性
            return this.$store.getters.dua_hasActiveStreaming;
        },

        // 获取流式消息索引
        streamingMessageIndex() {
            return this.$store.getters.dua_getStreamingMessageIndex;
        }
    },
    watch: {
        // 监听消息变化，处理流式响应和滚动
        messages: {
            handler() {
                // 检查是否有新的流式消息（现在由 Vuex 管理，无需手动设置）

                // 节流滚动到底部
                this.throttledScrollToBottom();
            },
            deep: true,
            immediate: false
        },
        // 监听 AI 响应状态变化
        isAiResponding: {
            handler() {
                this.throttledScrollToBottom();
            }
        },

        // 监听会话ID变化，处理状态恢复
        sessionId: {
            handler(newSessionId, oldSessionId) {
                if (newSessionId !== oldSessionId) {
                    // 会话切换时，流式消息索引由 Vuex 管理

                    // 滚动到底部
                    this.throttledScrollToBottom();
                }
            },
            immediate: false
        },

        // 监听流式响应状态变化
        hasActiveStreaming: {
            handler(newValue) {
                if (newValue) {
                    // 当前会话开始流式响应时，确保滚动到底部
                    this.throttledScrollToBottom();
                }
            }
        }
    },
    methods: {
        // 处理发送消息
        handleSendMessage(message) {
            // 添加用户类型标识
            const userMessage = {
                ...message,
                type: 'user'
            };
            this.$emit('send-message', userMessage);
        },

        // 处理停止生成
        handleStopGeneration() {
            this.$emit('stop-generation');
        },

        // 处理麦克风点击
        handleMicClick() {
            this.$emit('mic-click');
        },

        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // 滚动到底部
        scrollToBottom() {
            this.$nextTick(() => {
                const container = this.$refs.messagesContainer;
                if (container) {
                    // 使用平滑滚动动画
                    container.scrollTo({
                        top: container.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            });
        },

        // 处理滚动事件，控制回到底部按钮显示
        handleScroll() {
            // 使用节流优化性能
            if (this.scrollThrottleTimer) {
                clearTimeout(this.scrollThrottleTimer);
            }

            this.scrollThrottleTimer = setTimeout(() => {
                const container = this.$refs.messagesContainer;
                if (!container) return;

                const scrollTop = container.scrollTop;
                const scrollHeight = container.scrollHeight;
                const clientHeight = container.clientHeight;

                // 计算距离底部的距离
                const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

                // 显示条件：向下滚动超过400px且距离底部超过150px
                const shouldShow = distanceFromBottom > 150;

                this.showBackToBottom = shouldShow;
            }, 100); // 100ms节流
        },

        // 处理模型变更
        handleModelChange(newModel) {
            this.$emit('model-change', newModel);
        },

        // 检查是否为正在流式显示的消息
        isStreamingMessage(index) {
            return this.streamingMessageIndex === index;
        },

        // 获取要显示的文本内容（直接返回消息文本，无缓冲）
        getDisplayText(message) {
            return message.text || '';
        },

        // 节流滚动到底部
        throttledScrollToBottom() {
            const now = Date.now();
            if (now - this.lastScrollTime > 100) {
                // 每100ms最多滚动一次
                this.lastScrollTime = now;
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
            }
        },

        // 完成流式响应
        completeStreamResponse() {
            // 流式状态由 Vuex 管理，这里只需要滚动
            // 最后滚动一次到底部
            this.throttledScrollToBottom();
        },

        // 开始编辑标题
        startEditTitle() {
            // 只有在有会话ID且不是临时会话时才允许编辑
            if (!this.sessionId || this.sessionId.startsWith('temp_')) return;

            this.isEditingTitle = true;
            this.editingTitleValue = this.displayTitle;

            this.$nextTick(() => {
                if (this.$refs.titleInput) {
                    this.$refs.titleInput.focus();
                    this.$refs.titleInput.select();
                }
            });
        },

        // 保存标题
        saveTitle() {
            const newTitle = this.editingTitleValue.trim();
            if (newTitle && newTitle !== this.displayTitle && this.sessionId) {
                this.$emit('update-title', this.sessionId, newTitle);
            }
            this.cancelEditTitle();
        },

        // 取消编辑标题
        cancelEditTitle() {
            this.isEditingTitle = false;
            this.editingTitleValue = '';
        }
    },
    mounted() {
        // 添加滚动事件监听器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.addEventListener('scroll', this.handleScroll);
        }
    },
    beforeDestroy() {
        // 清理滚动事件监听器和定时器
        const container = this.$refs.messagesContainer;
        if (container) {
            container.removeEventListener('scroll', this.handleScroll);
        }
        if (this.scrollThrottleTimer) {
            clearTimeout(this.scrollThrottleTimer);
        }
    }
};
</script>

<style scoped lang="less">
.chat-page {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100vh;

    .chat-title-section {
        width: 100%;
        height: 3rem;
        background: rgba(255, 255, 255, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 1rem;

        .chat-title {
            font-weight: 500;
            font-size: 16px;
            color: #222222;
            line-height: 22px;
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            max-width: 100%;

            &:hover {
                background: rgba(0, 0, 0, 0.05);

                .edit-icon {
                    opacity: 1;
                }
            }

            &.temporary-session {
                color: #999;
                cursor: default;
                font-style: italic;

                &:hover {
                    background: transparent;
                }
            }

            .edit-icon {
                opacity: 0;
                transition: opacity 0.2s ease;
                font-size: 14px;
                color: #666;
            }
        }

        .chat-title-editor {
            max-width: 20rem;
            width: 100%;

            /deep/ .el-input__inner {
                text-align: center;
                font-weight: 500;
                font-size: 16px;
                border: 1px solid #0265fe;
                border-radius: 0.25rem;

                &:focus {
                    border-color: #0265fe;
                    box-shadow: 0 0 0 2px rgba(2, 101, 254, 0.2);
                }
            }
        }
    }

    .chat-container {
        min-height: 0;
        flex: 1;
        display: flex;
        align-items: center;
        flex-direction: column;
        margin: 0 auto;
        width: 100%;

        .chat-messages {
            --bar-width: 0;
            --bar-height: 0;
            width: 60rem;
            min-height: 0;
            flex: 1;
            overflow-y: auto;
            padding: 1rem 0;
            display: flex;
            flex-direction: column;
            gap: 1rem;

            .message-item {
                display: flex;

                &.user-message {
                    justify-content: flex-end;

                    .message-content {
                        background: #cde1ff;
                        border-radius: 0.75rem 0.25rem 0.75rem 0.75rem;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.assistant-message {
                    justify-content: flex-start;

                    .message-content {
                        background: transparent;
                        font-weight: 400;
                        font-size: 1rem;
                        color: #222222;
                        line-height: 1.5rem;
                    }
                }

                &.error-message {
                    justify-content: flex-start;

                    .message-content {
                        background: #fef2f2;
                        border: 1px solid #fecaca;
                        color: #dc2626;
                        max-width: 70%;
                    }
                }

                .message-content {
                    padding: 0.75rem 1rem;
                    border-radius: 0.75rem;

                    .message-text {
                        font-size: 0.875rem;
                        line-height: 1.6;
                        color: #333;
                    }

                    .message-time {
                        margin-top: 0.25rem;
                        font-size: 0.75rem;
                        opacity: 0.7;
                    }

                    // AI回复加载状态样式
                    &.loading-message {
                        display: flex;
                        flex-direction: column;
                        gap: 1rem;

                        .typing-text {
                            font-weight: 400;
                            font-size: 1rem;
                            color: #222222;
                            line-height: 1.5rem;
                        }

                        .typing-dots {
                            display: flex;
                            gap: 0.5rem;

                            .dot {
                                width: 0.5rem;
                                height: 0.5rem;
                                background: #666;
                                border-radius: 50%;
                                animation: typing 1.4s infinite ease-in-out;

                                &:nth-child(1) {
                                    animation-delay: 0s;
                                }
                                &:nth-child(2) {
                                    animation-delay: 0.2s;
                                }
                                &:nth-child(3) {
                                    animation-delay: 0.4s;
                                }
                            }
                        }

                        .stop-generation-btn {
                            width: fit-content;
                            margin-top: 0.5rem;
                            font-family: HONORSansCN, HONORSansCN;
                            font-weight: 400;
                            font-size: 1rem;
                            color: #0265fe;
                            line-height: 1.5rem;
                            cursor: pointer;
                            transition: color 0.2s;

                            &:hover {
                                color: lighten(#0265fe, 20%);
                            }
                        }
                    }
                }
            }

            .empty-state {
                flex: 1;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .empty-icon {
                    width: 4rem;
                    height: 4rem;
                    margin-bottom: 1rem;

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }

                .empty-text {
                    color: #999;
                    font-size: 0.875rem;
                }
            }
        }

        .chat-input-section {
            margin-top: auto;
            padding-bottom: 1.5rem;
            position: relative;
            .back-bottom {
                width: 2rem;
                height: 2rem;
                position: absolute;
                top: -1.25rem;
                right: 0.625rem;
                transform: translateY(-100%);
                cursor: pointer;
                border-radius: 50%;
                background-color: #fff;
                box-shadow: inset 0 0 0 0.0625rem darken(#fff, 5%);
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease-in-out;
                // 默认隐藏状态
                opacity: 0;
                visibility: hidden;
                transform: translateY(-100%) scale(0.8);

                // 显示状态
                &.is-show {
                    opacity: 1;
                    visibility: visible;
                    transform: translateY(-100%) scale(1);
                }

                &:hover {
                    background-color: darken(#fff, 5%);
                    transform: translateY(-100%) scale(1.05);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                }

                .el-icon-arrow-down {
                    font-size: 1rem;
                    font-weight: 600;
                    transition: transform 0.2s ease;
                }

                &:active {
                    transform: translateY(-100%) scale(0.95);
                }
            }
        }
    }
}

// 打字动画关键帧
@keyframes typing {
    0%,
    60%,
    100% {
        transform: scale(1);
        opacity: 0.5;
    }
    30% {
        transform: scale(1.2);
        opacity: 1;
    }
}

// 消息文本容器
.message-text-container {
    position: relative;
    display: inline-block;
    width: 100%;
}

// 流式文本样式
.streaming-text {
    position: relative;
    display: inline-block;
}

// 打字光标样式
.typing-cursor {
    display: inline-block;
    animation: blink 1s infinite;
    color: #0265fe;
    font-weight: bold;
    margin-left: 1px;
    vertical-align: baseline;
    font-size: 1em;
    line-height: 1;
}

// 内联光标样式（跟随文本）
.cursor-inline {
    position: relative;
    margin-left: 2px;
}

// 空消息占位符
.empty-message-placeholder {
    min-height: 20px;
    display: flex;
    align-items: center;
}

@keyframes blink {
    0%,
    50% {
        opacity: 1;
    }
    51%,
    100% {
        opacity: 0;
    }
}
</style>
<!-- Markdown相关样式已迁移到MarkdownRenderer组件 -->
