export default {
    state: {
        // 会话管理状态
        dua_currentSessionId: null,
        dua_chatSessions: [],
        dua_isStorageAvailable: false,
        dua_isTemporarySession: false,

        // 全局AI响应状态（仅用于全局状态追踪）
        dua_globalAiResponding: false,
        dua_globalStreamResponse: false,
        dua_globalRespondingSessionId: null,
        dua_globalCancelTokenSource: null,

        // 模型选择
        dua_selectedModel: 'DeepSeek',

        // 会话状态映射表 - 每个会话独立的状态管理
        dua_sessionStates: {
            // sessionId: {
            //     isAiResponding: false,
            //     isStreamResponse: false,
            //     cancelTokenSource: null,
            //     messages: [],
            //     model: 'DeepSeek',
            //     streamingMessageIndex: -1,
            //     lastUpdateTime: timestamp,
            //     isTemporary: false
            // }
        },

        // UI状态
        dua_currentView: 'home',
        dua_activeMenuItem: null,
        dua_chatMessages: []
    },
    getters: {
        // 会话状态相关getters
        dua_getCurrentSession: (state) => {
            return state.dua_currentSessionId;
        },

        dua_getSessionState: (state) => (sessionId) => {
            return state.dua_sessionStates[sessionId] || null;
        },

        // 获取当前会话的状态
        dua_getCurrentSessionState: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return null;
            return state.dua_sessionStates[currentId] || null;
        },

        // 当前会话是否正在响应（严格会话隔离）
        dua_isCurrentSessionResponding: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return false;

            const sessionState = state.dua_sessionStates[currentId];
            if (sessionState) {
                return sessionState.isAiResponding;
            }
            return false;
        },

        // 当前会话是否正在流式响应（严格会话隔离）
        dua_isCurrentSessionStreaming: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return false;

            const sessionState = state.dua_sessionStates[currentId];
            if (sessionState) {
                return sessionState.isStreamResponse;
            }
            return false;
        },

        // 是否应该显示加载状态（严格会话隔离）
        dua_shouldShowLoadingState: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return false;

            const sessionState = state.dua_sessionStates[currentId];
            if (sessionState) {
                return sessionState.isAiResponding;
            }
            return false;
        },

        // 当前会话是否有活跃的流式响应（严格会话隔离）
        dua_hasActiveStreaming: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return false;

            const sessionState = state.dua_sessionStates[currentId];
            if (!sessionState || !sessionState.isStreamResponse) {
                return false;
            }

            // 检查当前会话的消息中是否有正在流式显示的消息
            if (state.dua_chatMessages.length === 0) {
                return false;
            }

            // 检查最后一条消息是否为当前会话的流式消息
            const lastMessage = state.dua_chatMessages[state.dua_chatMessages.length - 1];
            return lastMessage &&
                lastMessage.type === 'assistant' &&
                lastMessage.isStreaming === true &&
                lastMessage.sessionId === currentId; // 确保消息属于当前会话
        },

        // 获取当前会话的流式消息索引（严格会话隔离）
        dua_getStreamingMessageIndex: (state) => {
            const currentId = state.dua_currentSessionId;
            if (!currentId) return -1;

            const sessionState = state.dua_sessionStates[currentId];
            if (!sessionState || !sessionState.isStreamResponse) {
                return -1;
            }

            const messages = state.dua_chatMessages;
            if (messages.length === 0) {
                return -1;
            }

            // 从最后一条消息开始检查，找到正在流式显示的助手消息
            for (let i = messages.length - 1; i >= 0; i--) {
                if (messages[i].type === 'assistant' &&
                    messages[i].isStreaming === true &&
                    messages[i].sessionId === currentId) { // 确保消息属于当前会话
                    return i;
                }
            }
            return -1;
        },

        // 获取全局AI响应状态（用于调试和监控）
        dua_getGlobalAiState: (state) => {
            return {
                isResponding: state.dua_globalAiResponding,
                isStreaming: state.dua_globalStreamResponse,
                respondingSessionId: state.dua_globalRespondingSessionId
            };
        }
    },
    mutations: {
        // 会话管理mutations
        DUA_SET_CURRENT_SESSION(state, sessionId) {
            state.dua_currentSessionId = sessionId;
        },

        DUA_SET_CHAT_SESSIONS(state, sessions) {
            state.dua_chatSessions = sessions;
        },

        DUA_SET_STORAGE_AVAILABLE(state, available) {
            state.dua_isStorageAvailable = available;
        },

        DUA_SET_TEMPORARY_SESSION(state, isTemporary) {
            state.dua_isTemporarySession = isTemporary;
        },

        DUA_SET_CHAT_MESSAGES(state, messages) {
            state.dua_chatMessages = messages;
        },

        DUA_ADD_CHAT_MESSAGE(state, message) {
            // 确保消息有会话ID标记
            const messageWithSessionId = {
                ...message,
                sessionId: message.sessionId || state.dua_currentSessionId
            };
            state.dua_chatMessages.push(messageWithSessionId);
        },

        DUA_UPDATE_CHAT_MESSAGE(state, { index, message }) {
            if (index >= 0 && index < state.dua_chatMessages.length) {
                state.dua_chatMessages.splice(index, 1, message);
            }
        },

        // 会话级状态管理mutations
        DUA_INIT_SESSION_STATE(state, { sessionId, sessionData }) {
            if (!sessionId) return;

            state.dua_sessionStates[sessionId] = {
                isAiResponding: false,
                isStreamResponse: false,
                cancelTokenSource: null,
                messages: sessionData.messages || [],
                model: sessionData.model || 'DeepSeek',
                streamingMessageIndex: -1,
                lastUpdateTime: Date.now(),
                isTemporary: sessionData.isTemporary || false,
                ...sessionData
            };
        },

        DUA_UPDATE_SESSION_STATE(state, { sessionId, updates }) {
            if (!sessionId || !state.dua_sessionStates[sessionId]) return;

            state.dua_sessionStates[sessionId] = {
                ...state.dua_sessionStates[sessionId],
                ...updates,
                lastUpdateTime: Date.now()
            };
        },

        DUA_REMOVE_SESSION_STATE(state, sessionId) {
            if (sessionId && state.dua_sessionStates[sessionId]) {
                delete state.dua_sessionStates[sessionId];
            }
        },

        // 全局AI响应状态mutations（用于全局状态追踪）
        DUA_SET_GLOBAL_AI_RESPONDING(state, responding) {
            state.dua_globalAiResponding = responding;
        },

        DUA_SET_GLOBAL_STREAM_RESPONSE(state, streaming) {
            state.dua_globalStreamResponse = streaming;
        },

        DUA_SET_GLOBAL_RESPONDING_SESSION_ID(state, sessionId) {
            state.dua_globalRespondingSessionId = sessionId;
        },

        DUA_SET_GLOBAL_CANCEL_TOKEN_SOURCE(state, source) {
            state.dua_globalCancelTokenSource = source;
        },

        // 会话级AI状态mutations
        DUA_SET_SESSION_AI_RESPONDING(state, { sessionId, responding }) {
            if (!sessionId || !state.dua_sessionStates[sessionId]) return;

            state.dua_sessionStates[sessionId].isAiResponding = responding;
            state.dua_sessionStates[sessionId].lastUpdateTime = Date.now();
        },

        DUA_SET_SESSION_STREAM_RESPONSE(state, { sessionId, streaming }) {
            if (!sessionId || !state.dua_sessionStates[sessionId]) return;

            state.dua_sessionStates[sessionId].isStreamResponse = streaming;
            state.dua_sessionStates[sessionId].lastUpdateTime = Date.now();
        },

        DUA_SET_SESSION_CANCEL_TOKEN(state, { sessionId, cancelTokenSource }) {
            if (!sessionId || !state.dua_sessionStates[sessionId]) return;

            state.dua_sessionStates[sessionId].cancelTokenSource = cancelTokenSource;
            state.dua_sessionStates[sessionId].lastUpdateTime = Date.now();
        },

        DUA_SET_SELECTED_MODEL(state, model) {
            state.dua_selectedModel = model;
        },

        // UI状态mutations
        DUA_SET_CURRENT_VIEW(state, view) {
            state.dua_currentView = view;
        },

        DUA_SET_ACTIVE_MENU_ITEM(state, item) {
            state.dua_activeMenuItem = item;
        },

        // 重置全局AI状态
        DUA_RESET_GLOBAL_AI_STATE(state) {
            state.dua_globalAiResponding = false;
            state.dua_globalStreamResponse = false;
            state.dua_globalRespondingSessionId = null;
            state.dua_globalCancelTokenSource = null;
        },

        // 重置会话AI状态
        DUA_RESET_SESSION_AI_STATE(state, sessionId) {
            if (!sessionId || !state.dua_sessionStates[sessionId]) return;

            state.dua_sessionStates[sessionId].isAiResponding = false;
            state.dua_sessionStates[sessionId].isStreamResponse = false;
            state.dua_sessionStates[sessionId].cancelTokenSource = null;
            state.dua_sessionStates[sessionId].streamingMessageIndex = -1;
            state.dua_sessionStates[sessionId].lastUpdateTime = Date.now();
        },

        // 清理过期的会话状态
        DUA_CLEAR_EXPIRED_SESSION_STATES(state) {
            const now = Date.now();
            const expireTime = 30 * 60 * 1000; // 30分钟

            Object.keys(state.dua_sessionStates).forEach(sessionId => {
                const sessionState = state.dua_sessionStates[sessionId];
                if (sessionState && sessionState.lastUpdateTime &&
                    now - sessionState.lastUpdateTime > expireTime) {
                    delete state.dua_sessionStates[sessionId];
                }
            });
        }
    },
    actions: {
        // 初始化会话状态
        dua_initSessionState({ commit }, { sessionId, sessionData = {} }) {
            if (!sessionId) return;

            commit('DUA_INIT_SESSION_STATE', { sessionId, sessionData });
        },

        // 保存当前会话状态
        dua_saveCurrentSessionState({ commit, state }) {
            const currentSessionId = state.dua_currentSessionId;
            if (!currentSessionId) return;

            const currentSessionState = state.dua_sessionStates[currentSessionId];
            if (!currentSessionState) return;

            // 保存流式内容到会话状态
            if (currentSessionState.isStreamResponse && state.dua_chatMessages.length > 0) {
                const lastMessage = state.dua_chatMessages[state.dua_chatMessages.length - 1];
                if (lastMessage && lastMessage.type === 'assistant') {
                    const updates = {
                        streamingContent: {
                            text: lastMessage.text || '',
                            isStreaming: lastMessage.isStreaming || false
                        },
                        messages: [...state.dua_chatMessages]
                    };
                    commit('DUA_UPDATE_SESSION_STATE', { sessionId: currentSessionId, updates });
                }
            } else {
                // 保存普通消息状态
                const updates = {
                    messages: [...state.dua_chatMessages]
                };
                commit('DUA_UPDATE_SESSION_STATE', { sessionId: currentSessionId, updates });
            }
        },

        // 恢复会话状态（严格会话隔离）
        dua_restoreSessionState({ commit, state }, sessionId) {
            if (!sessionId) return;

            const sessionState = state.dua_sessionStates[sessionId];
            if (!sessionState) return;

            // 检查状态是否过期
            if (sessionState.lastUpdateTime &&
                Date.now() - sessionState.lastUpdateTime > 30 * 60 * 1000) {
                commit('DUA_REMOVE_SESSION_STATE', sessionId);
                return;
            }

            // 恢复流式内容（如果存在）
            if (sessionState.streamingContent && state.dua_chatMessages.length > 0) {
                const lastMessage = state.dua_chatMessages[state.dua_chatMessages.length - 1];
                if (lastMessage && lastMessage.type === 'assistant') {
                    const updatedMessage = {
                        ...lastMessage,
                        text: sessionState.streamingContent.text,
                        isStreaming: sessionState.streamingContent.isStreaming
                    };
                    const messageIndex = state.dua_chatMessages.length - 1;
                    commit('DUA_UPDATE_CHAT_MESSAGE', { index: messageIndex, message: updatedMessage });
                }
            }

            // 同步全局状态（仅当这是当前会话时）
            if (sessionId === state.dua_currentSessionId) {
                commit('DUA_SET_GLOBAL_AI_RESPONDING', sessionState.isAiResponding);
                commit('DUA_SET_GLOBAL_STREAM_RESPONSE', sessionState.isStreamResponse);
                commit('DUA_SET_GLOBAL_RESPONDING_SESSION_ID', sessionId);
                commit('DUA_SET_GLOBAL_CANCEL_TOKEN_SOURCE', sessionState.cancelTokenSource);
            }
        },

        // 清理过期的会话状态
        dua_cleanupExpiredSessionStates({ commit }) {
            commit('DUA_CLEAR_EXPIRED_SESSION_STATES');
        },

        // 开始AI响应（会话级）
        dua_startAiResponse({ commit, dispatch }, { sessionId, cancelTokenSource }) {
            if (!sessionId) return;

            // 确保会话状态存在
            dispatch('dua_ensureSessionState', sessionId);

            // 设置会话级AI状态
            commit('DUA_SET_SESSION_AI_RESPONDING', { sessionId, responding: true });
            commit('DUA_SET_SESSION_CANCEL_TOKEN', { sessionId, cancelTokenSource });

            // 同步全局状态
            commit('DUA_SET_GLOBAL_AI_RESPONDING', true);
            commit('DUA_SET_GLOBAL_RESPONDING_SESSION_ID', sessionId);
            commit('DUA_SET_GLOBAL_CANCEL_TOKEN_SOURCE', cancelTokenSource);
        },

        // 开始流式响应（会话级）
        dua_startStreamResponse({ commit, state }) {
            const currentSessionId = state.dua_currentSessionId;
            if (!currentSessionId) return;

            // 设置会话级流式状态
            commit('DUA_SET_SESSION_AI_RESPONDING', { sessionId: currentSessionId, responding: false });
            commit('DUA_SET_SESSION_STREAM_RESPONSE', { sessionId: currentSessionId, streaming: true });

            // 同步全局状态
            commit('DUA_SET_GLOBAL_AI_RESPONDING', false);
            commit('DUA_SET_GLOBAL_STREAM_RESPONSE', true);
        },

        // 完成响应（会话级）
        dua_completeResponse({ commit, state }) {
            const currentSessionId = state.dua_currentSessionId;

            // 重置全局状态
            commit('DUA_RESET_GLOBAL_AI_STATE');

            // 重置当前会话状态
            if (currentSessionId) {
                commit('DUA_RESET_SESSION_AI_STATE', currentSessionId);
            }
        },

        // 取消响应（会话级）
        dua_cancelResponse({ dispatch, commit, state }) {
            const currentSessionId = state.dua_currentSessionId;

            // 保存当前状态
            dispatch('dua_saveCurrentSessionState');

            // 重置全局状态
            commit('DUA_RESET_GLOBAL_AI_STATE');

            // 重置当前会话的AI状态，确保UI状态正确
            if (currentSessionId) {
                commit('DUA_RESET_SESSION_AI_STATE', currentSessionId);
            }
        },

        // 确保会话状态存在
        dua_ensureSessionState({ commit, state }, sessionId) {
            if (!sessionId || state.dua_sessionStates[sessionId]) return;

            commit('DUA_INIT_SESSION_STATE', {
                sessionId,
                sessionData: {
                    isTemporary: sessionId.startsWith('temp_')
                }
            });
        },

        // 切换会话（完整的会话切换逻辑）
        dua_switchSession({ commit, dispatch, state }, { sessionId, sessionData }) {
            if (!sessionId) return;

            // 保存当前会话状态
            if (state.dua_currentSessionId) {
                dispatch('dua_saveCurrentSessionState');
            }

            // 确保目标会话状态存在
            dispatch('dua_ensureSessionState', sessionId);

            // 更新会话信息
            commit('DUA_SET_CURRENT_SESSION', sessionId);

            // 确保消息都有正确的会话ID标记
            const messagesWithSessionId = (sessionData.messages || []).map(message => ({
                ...message,
                sessionId: sessionId // 确保每条消息都有会话ID标记
            }));
            commit('DUA_SET_CHAT_MESSAGES', messagesWithSessionId);

            commit('DUA_SET_SELECTED_MODEL', sessionData.model || 'DeepSeek');
            commit('DUA_SET_ACTIVE_MENU_ITEM', sessionId);
            commit('DUA_SET_TEMPORARY_SESSION', sessionId.startsWith('temp_'));

            // 恢复会话状态
            dispatch('dua_restoreSessionState', sessionId);
        }
    },
};